{"wandb_key": "****************************************", "local_rank": 0, "precision": "fp32", "ds_zero": "zero2", "load_in_8bit": false, "load_in_4bit": false, "attn_imple": "eager", "liger_kernel": false, "model_id": "showlab/ShowUI-2B", "version": "showlab/ShowUI-2B", "max_new_tokens": 128, "local_weight": true, "local_weight_dir": "../ShowUI-2B", "tune_visual_encoder": false, "tune_visual_encoder_projector": false, "freeze_lm_embed": false, "dataset_dir": "../showui_data", "train_dataset": "my_ui_grounding_dataset", "train_json": "hf_train", "train_ratio": "1", "val_dataset": "none", "val_json": "hf_test_full", "val_ratio": "1", "uniform_sample": false, "random_sample": false, "record_sample": false, "min_visual_tokens": 256, "max_visual_tokens": 1280, "model_max_length": 8192, "uigraph_train": true, "uigraph_test": false, "uigraph_diff": 1, "uigraph_rand": false, "uimask_pre": true, "uimask_ratio": 0.5, "uimask_rand": false, "lm_skip_ratio": 0, "lm_skip_layer": "[1,28,0]", "vis_skip_ratio": 0, "vis_skip_layer": "[1,32,0]", "showui_data": "hf_train", "amex_data": "hf_train", "guiact_data": "hf_train_web-single_v2", "ricosca_data": "hf_train_ricosca", "widget_data": "hf_train_widget", "screencap_data": "hf_train_screencap", "aitw_data": "hf_train", "mind2web_data": "hf_train", "miniwob_data": "hf_train", "val_aitw_data": "hf_test", "val_mind2web_data": "hf_test_full", "val_screenspot_data": "hf_test_full", "num_turn": 1, "shuffle_image_token": false, "uniform_prompt": false, "text2point": 1, "text2bbox": 0, "point2text": 0, "bbox2text": 0, "crop_min": 1, "crop_max": 1, "xy_int": false, "num_history": 4, "interleaved_history": "tttt", "skip_readme_train": false, "skip_readme_test": false, "use_qlora": false, "lora_r": 8, "lora_alpha": 16, "lora_dropout": 0.05, "lora_target_modules": "qkv_proj", "log_base_dir": "../runs", "exp_id": "test_run", "workers": 1, "epochs": 1, "start_epoch": 0, "steps_per_epoch": 2, "lr": 0.0001, "warmup_steps": 1, "warmup_type": "linear", "beta1": 0.9, "beta2": 0.95, "batch_size": 1, "grad_accumulation_steps": 1, "val_batch_size": 1, "gradient_checkpointing": true, "resume": "", "auto_resume": true, "no_eval": true, "eval_only": false, "print_freq": 1, "debug": true, "global_rank": 0, "world_size": 1, "distributed": false, "log_dir": "../runs/test_run/2025-06-12_21-35-21", "tmp_dir": "../runs/test_run/2025-06-12_21-35-21/tmp"}