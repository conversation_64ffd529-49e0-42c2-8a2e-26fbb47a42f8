2025-06-12 17:10:28,614 INFO    MainThread:3802 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-06-12 17:10:28,614 INFO    MainThread:3802 [wandb_setup.py:_flush():81] Configure stats pid to 3802
2025-06-12 17:10:28,614 INFO    MainThread:3802 [wandb_setup.py:_flush():81] Loading settings from /Users/<USER>/.config/wandb/settings
2025-06-12 17:10:28,614 INFO    MainThread:3802 [wandb_setup.py:_flush():81] Loading settings from /Users/<USER>/Desktop/学习/Project/show_ui/ShowUI-main/wandb/settings
2025-06-12 17:10:28,614 INFO    MainThread:3802 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-12 17:10:28,614 INFO    MainThread:3802 [wandb_init.py:setup_run_log_directory():703] Logging user logs to ../runs/debug/2025-06-12_17-10-27/wandb/run-20250612_171028-tf4o6ulp/logs/debug.log
2025-06-12 17:10:28,614 INFO    MainThread:3802 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to ../runs/debug/2025-06-12_17-10-27/wandb/run-20250612_171028-tf4o6ulp/logs/debug-internal.log
2025-06-12 17:10:28,614 INFO    MainThread:3802 [wandb_init.py:init():831] calling init triggers
2025-06-12 17:10:28,614 INFO    MainThread:3802 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'wandb_key': '****************************************', 'local_rank': 0, 'precision': 'bf16', 'ds_zero': 'zero2', 'load_in_8bit': False, 'load_in_4bit': False, 'attn_imple': 'eager', 'liger_kernel': False, 'model_id': 'showlab/ShowUI-2B', 'version': 'showlab/ShowUI-2B', 'max_new_tokens': 128, 'local_weight': False, 'local_weight_dir': '.', 'tune_visual_encoder': False, 'tune_visual_encoder_projector': False, 'freeze_lm_embed': False, 'dataset_dir': './dataset', 'train_dataset': 'showui', 'train_json': 'hf_train', 'train_ratio': '1', 'val_dataset': 'screenspot', 'val_json': 'hf_test_full', 'val_ratio': '1', 'uniform_sample': False, 'random_sample': False, 'record_sample': False, 'min_visual_tokens': 256, 'max_visual_tokens': 1280, 'model_max_length': 8192, 'uigraph_train': True, 'uigraph_test': False, 'uigraph_diff': 1, 'uigraph_rand': False, 'uimask_pre': True, 'uimask_ratio': 0.5, 'uimask_rand': False, 'lm_skip_ratio': 0, 'lm_skip_layer': '[1,28,0]', 'vis_skip_ratio': 0, 'vis_skip_layer': '[1,32,0]', 'showui_data': 'hf_train', 'amex_data': 'hf_train', 'guiact_data': 'hf_train_web-single_v2', 'ricosca_data': 'hf_train_ricosca', 'widget_data': 'hf_train_widget', 'screencap_data': 'hf_train_screencap', 'aitw_data': 'hf_train', 'mind2web_data': 'hf_train', 'miniwob_data': 'hf_train', 'val_aitw_data': 'hf_test', 'val_mind2web_data': 'hf_test_full', 'val_screenspot_data': 'hf_test_full', 'num_turn': 1, 'shuffle_image_token': False, 'uniform_prompt': False, 'text2point': 1, 'text2bbox': 0, 'point2text': 0, 'bbox2text': 0, 'crop_min': 1, 'crop_max': 1, 'xy_int': False, 'num_history': 4, 'interleaved_history': 'tttt', 'skip_readme_train': False, 'skip_readme_test': False, 'use_qlora': False, 'lora_r': 8, 'lora_alpha': 16, 'lora_dropout': 0.05, 'lora_target_modules': 'qkv_proj', 'log_base_dir': '../runs', 'exp_id': 'debug', 'workers': 16, 'epochs': 10, 'start_epoch': 0, 'steps_per_epoch': 500, 'lr': 0.0003, 'warmup_steps': 100, 'warmup_type': 'linear', 'beta1': 0.9, 'beta2': 0.95, 'batch_size': 1, 'grad_accumulation_steps': 1, 'val_batch_size': 1, 'gradient_checkpointing': False, 'resume': '', 'auto_resume': True, 'no_eval': False, 'eval_only': False, 'print_freq': 1, 'debug': False, 'global_rank': 0, 'world_size': 1, 'distributed': False, 'log_dir': '../runs/debug/2025-06-12_17-10-27', 'tmp_dir': '../runs/debug/2025-06-12_17-10-27/tmp', '_wandb': {}}
2025-06-12 17:10:28,615 INFO    MainThread:3802 [wandb_init.py:init():872] starting backend
2025-06-12 17:10:28,836 INFO    MainThread:3802 [wandb_init.py:init():875] sending inform_init request
2025-06-12 17:10:28,866 INFO    MainThread:3802 [wandb_init.py:init():883] backend started and connected
2025-06-12 17:10:28,869 INFO    MainThread:3802 [wandb_init.py:init():956] updated telemetry
2025-06-12 17:10:28,870 INFO    MainThread:3802 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-12 17:10:29,638 INFO    MainThread:3802 [wandb_init.py:init():1032] starting run threads in backend
2025-06-12 17:10:29,718 INFO    MainThread:3802 [wandb_run.py:_console_start():2453] atexit reg
2025-06-12 17:10:29,718 INFO    MainThread:3802 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-12 17:10:29,719 INFO    MainThread:3802 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-12 17:10:29,719 INFO    MainThread:3802 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-12 17:10:29,720 INFO    MainThread:3802 [wandb_init.py:init():1078] run started, returning control to user process
2025-06-12 17:10:30,029 INFO    MsgRouterThr:3802 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
