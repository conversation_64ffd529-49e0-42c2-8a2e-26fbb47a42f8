_wandb:
    value:
        cli_version: 0.20.1
        m: []
        python_version: 3.9.6
        t:
            "1":
                - 1
                - 11
                - 12
                - 41
                - 49
                - 71
                - 98
                - 105
            "2":
                - 1
                - 11
                - 12
                - 41
                - 49
                - 71
                - 98
                - 105
            "3":
                - 13
                - 16
                - 55
            "4": 3.9.6
            "5": 0.20.1
            "6": 4.36.2
            "12": 0.20.1
            "13": darwin-arm64
aitw_data:
    value: hf_train
amex_data:
    value: hf_train
attn_imple:
    value: eager
auto_resume:
    value: true
batch_size:
    value: 1
bbox2text:
    value: 0
beta1:
    value: 0.9
beta2:
    value: 0.95
crop_max:
    value: 1
crop_min:
    value: 1
dataset_dir:
    value: ./dataset
debug:
    value: false
distributed:
    value: false
ds_zero:
    value: zero2
epochs:
    value: 10
eval_only:
    value: false
exp_id:
    value: debug
freeze_lm_embed:
    value: false
global_rank:
    value: 0
grad_accumulation_steps:
    value: 1
gradient_checkpointing:
    value: false
guiact_data:
    value: hf_train_web-single_v2
interleaved_history:
    value: tttt
liger_kernel:
    value: false
lm_skip_layer:
    value: '[1,28,0]'
lm_skip_ratio:
    value: 0
load_in_4bit:
    value: false
load_in_8bit:
    value: false
local_rank:
    value: 0
local_weight:
    value: false
local_weight_dir:
    value: .
log_base_dir:
    value: ../runs
log_dir:
    value: ../runs/debug/2025-06-12_17-10-27
lora_alpha:
    value: 16
lora_dropout:
    value: 0.05
lora_r:
    value: 8
lora_target_modules:
    value: qkv_proj
lr:
    value: 0.0003
max_new_tokens:
    value: 128
max_visual_tokens:
    value: 1280
min_visual_tokens:
    value: 256
mind2web_data:
    value: hf_train
miniwob_data:
    value: hf_train
model_id:
    value: showlab/ShowUI-2B
model_max_length:
    value: 8192
no_eval:
    value: false
num_history:
    value: 4
num_turn:
    value: 1
point2text:
    value: 0
precision:
    value: bf16
print_freq:
    value: 1
random_sample:
    value: false
record_sample:
    value: false
resume:
    value: ""
ricosca_data:
    value: hf_train_ricosca
screencap_data:
    value: hf_train_screencap
showui_data:
    value: hf_train
shuffle_image_token:
    value: false
skip_readme_test:
    value: false
skip_readme_train:
    value: false
start_epoch:
    value: 0
steps_per_epoch:
    value: 500
text2bbox:
    value: 0
text2point:
    value: 1
tmp_dir:
    value: ../runs/debug/2025-06-12_17-10-27/tmp
train_dataset:
    value: showui
train_json:
    value: hf_train
train_ratio:
    value: "1"
tune_visual_encoder:
    value: false
tune_visual_encoder_projector:
    value: false
uigraph_diff:
    value: 1
uigraph_rand:
    value: false
uigraph_test:
    value: false
uigraph_train:
    value: true
uimask_pre:
    value: true
uimask_rand:
    value: false
uimask_ratio:
    value: 0.5
uniform_prompt:
    value: false
uniform_sample:
    value: false
use_qlora:
    value: false
val_aitw_data:
    value: hf_test
val_batch_size:
    value: 1
val_dataset:
    value: screenspot
val_json:
    value: hf_test_full
val_mind2web_data:
    value: hf_test_full
val_ratio:
    value: "1"
val_screenspot_data:
    value: hf_test_full
version:
    value: showlab/ShowUI-2B
vis_skip_layer:
    value: '[1,32,0]'
vis_skip_ratio:
    value: 0
wandb_key:
    value: ****************************************
warmup_steps:
    value: 100
warmup_type:
    value: linear
widget_data:
    value: hf_train_widget
workers:
    value: 16
world_size:
    value: 1
xy_int:
    value: false
