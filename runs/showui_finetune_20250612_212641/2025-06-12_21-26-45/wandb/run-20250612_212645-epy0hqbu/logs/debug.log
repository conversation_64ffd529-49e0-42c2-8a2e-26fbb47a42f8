2025-06-12 21:26:45,983 INFO    MainThread:9422 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-06-12 21:26:45,984 INFO    MainThread:9422 [wandb_setup.py:_flush():81] Configure stats pid to 9422
2025-06-12 21:26:45,984 INFO    MainThread:9422 [wandb_setup.py:_flush():81] Loading settings from /Users/<USER>/.config/wandb/settings
2025-06-12 21:26:45,984 INFO    MainThread:9422 [wandb_setup.py:_flush():81] Loading settings from /Users/<USER>/Desktop/学习/Project/show_ui/ShowUI-main/wandb/settings
2025-06-12 21:26:45,984 INFO    MainThread:9422 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-12 21:26:45,984 INFO    MainThread:9422 [wandb_init.py:setup_run_log_directory():703] Logging user logs to ../runs/showui_finetune_20250612_212641/2025-06-12_21-26-45/wandb/run-20250612_212645-epy0hqbu/logs/debug.log
2025-06-12 21:26:45,984 INFO    MainThread:9422 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to ../runs/showui_finetune_20250612_212641/2025-06-12_21-26-45/wandb/run-20250612_212645-epy0hqbu/logs/debug-internal.log
2025-06-12 21:26:45,984 INFO    MainThread:9422 [wandb_init.py:init():831] calling init triggers
2025-06-12 21:26:45,984 INFO    MainThread:9422 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'wandb_key': '****************************************', 'local_rank': 0, 'precision': 'fp16', 'ds_zero': 'zero2', 'load_in_8bit': False, 'load_in_4bit': False, 'attn_imple': 'eager', 'liger_kernel': False, 'model_id': 'showlab/ShowUI-2B', 'version': 'showlab/ShowUI-2B', 'max_new_tokens': 128, 'local_weight': True, 'local_weight_dir': '../ShowUI-2B', 'tune_visual_encoder': False, 'tune_visual_encoder_projector': False, 'freeze_lm_embed': False, 'dataset_dir': '../showui_data', 'train_dataset': '../showui_data/my_ui_grounding_dataset', 'train_json': '../showui_data/my_ui_grounding_dataset/metadata/hf_train.json', 'train_ratio': '1', 'val_dataset': 'screenspot', 'val_json': 'hf_test_full', 'val_ratio': '1', 'uniform_sample': False, 'random_sample': False, 'record_sample': False, 'min_visual_tokens': 256, 'max_visual_tokens': 1280, 'model_max_length': 8192, 'uigraph_train': True, 'uigraph_test': False, 'uigraph_diff': 1, 'uigraph_rand': False, 'uimask_pre': True, 'uimask_ratio': 0.5, 'uimask_rand': False, 'lm_skip_ratio': 0, 'lm_skip_layer': '[1,28,0]', 'vis_skip_ratio': 0, 'vis_skip_layer': '[1,32,0]', 'showui_data': 'hf_train', 'amex_data': 'hf_train', 'guiact_data': 'hf_train_web-single_v2', 'ricosca_data': 'hf_train_ricosca', 'widget_data': 'hf_train_widget', 'screencap_data': 'hf_train_screencap', 'aitw_data': 'hf_train', 'mind2web_data': 'hf_train', 'miniwob_data': 'hf_train', 'val_aitw_data': 'hf_test', 'val_mind2web_data': 'hf_test_full', 'val_screenspot_data': 'hf_test_full', 'num_turn': 1, 'shuffle_image_token': False, 'uniform_prompt': False, 'text2point': 1, 'text2bbox': 0, 'point2text': 0, 'bbox2text': 0, 'crop_min': 1, 'crop_max': 1, 'xy_int': False, 'num_history': 4, 'interleaved_history': 'tttt', 'skip_readme_train': False, 'skip_readme_test': False, 'use_qlora': True, 'lora_r': 8, 'lora_alpha': 16, 'lora_dropout': 0.05, 'lora_target_modules': 'qkv_proj', 'log_base_dir': '../runs', 'exp_id': 'showui_finetune_20250612_212641', 'workers': 4, 'epochs': 3, 'start_epoch': 0, 'steps_per_epoch': 100, 'lr': 0.0001, 'warmup_steps': 50, 'warmup_type': 'linear', 'beta1': 0.9, 'beta2': 0.95, 'batch_size': 1, 'grad_accumulation_steps': 4, 'val_batch_size': 1, 'gradient_checkpointing': True, 'resume': '', 'auto_resume': True, 'no_eval': True, 'eval_only': False, 'print_freq': 10, 'debug': False, 'global_rank': 0, 'world_size': 1, 'distributed': False, 'log_dir': '../runs/showui_finetune_20250612_212641/2025-06-12_21-26-45', 'tmp_dir': '../runs/showui_finetune_20250612_212641/2025-06-12_21-26-45/tmp', '_wandb': {}}
2025-06-12 21:26:45,984 INFO    MainThread:9422 [wandb_init.py:init():872] starting backend
2025-06-12 21:26:46,207 INFO    MainThread:9422 [wandb_init.py:init():875] sending inform_init request
2025-06-12 21:26:46,229 INFO    MainThread:9422 [wandb_init.py:init():883] backend started and connected
2025-06-12 21:26:46,233 INFO    MainThread:9422 [wandb_init.py:init():956] updated telemetry
2025-06-12 21:26:46,233 INFO    MainThread:9422 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-12 21:26:47,191 INFO    MainThread:9422 [wandb_init.py:init():1032] starting run threads in backend
2025-06-12 21:26:47,271 INFO    MainThread:9422 [wandb_run.py:_console_start():2453] atexit reg
2025-06-12 21:26:47,271 INFO    MainThread:9422 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-12 21:26:47,272 INFO    MainThread:9422 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-12 21:26:47,272 INFO    MainThread:9422 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-12 21:26:47,273 INFO    MainThread:9422 [wandb_init.py:init():1078] run started, returning control to user process
2025-06-12 21:26:47,589 INFO    MsgRouterThr:9422 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
