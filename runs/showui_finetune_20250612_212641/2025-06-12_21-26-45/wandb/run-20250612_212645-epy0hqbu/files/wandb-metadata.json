{"os": "macOS-15.5-arm64-arm-64bit", "python": "CPython 3.9.6", "startedAt": "2025-06-12T13:26:46.230057Z", "args": ["--model_id", "showlab/ShowUI-2B", "--local_weight", "--local_weight_dir", "../ShowUI-2B", "--train_dataset", "../showui_data/my_ui_grounding_dataset", "--train_json", "../showui_data/my_ui_grounding_dataset/metadata/hf_train.json", "--dataset_dir", "../showui_data", "--exp_id", "showui_finetune_20250612_212641", "--epochs", "3", "--steps_per_epoch", "100", "--batch_size", "1", "--grad_accumulation_steps", "4", "--lr", "0.0001", "--warmup_steps", "50", "--workers", "4", "--precision", "fp16", "--use_qlora", "--lora_r", "8", "--lora_alpha", "16", "--gradient_checkpointing", "--no_eval", "--print_freq", "10", "--log_base_dir", "../runs"], "program": "/Users/<USER>/Desktop/学习/Project/show_ui/ShowUI-main/train.py", "codePath": "train.py", "email": "<EMAIL>", "root": "../runs/showui_finetune_20250612_212641/2025-06-12_21-26-45", "host": "wuzhongrundeMacBook-Pro.local", "executable": "/Users/<USER>/Desktop/学习/Project/show_ui/.venv/bin/python", "codePathLocal": "train.py"}