shellingham==1.5.4
semantic-version==2.10.0
hf-xet==1.1.3
MouseInfo==0.1.3
starlette==0.46.2
pydantic_core==2.33.2
pexpect==4.9.0
Pygments==2.19.1
tomlkit==0.12.0
PyScreeze==1.0.1
mss==10.0.0
idna==3.10
typing_extensions==4.14.0
traitlets==5.14.3
narwhals==1.42.0
pydub==0.25.1
typer==0.16.0
gradio==3.50.2
ptyprocess==0.7.0
pytz==2025.2
scipy==1.13.1
rubicon-objc==0.5.1
matplotlib==3.9.4
protobuf==6.31.1
typing-inspection==0.4.1
parso==0.8.4
PyMsgBox==1.0.9
pyobjc-framework-Cocoa==11.0
pip==25.1.1
platformdirs==4.3.8
opt_einsum==3.4.0
anyio==4.9.0
attrs==25.3.0
markdown-it-py==3.0.0
bitsandbytes==0.42.0
pydantic==2.11.5
websockets==11.0.3
GitPython==3.1.44
pyparsing==3.2.3
jedi==0.19.2
exceptiongroup==1.3.0
charset-normalizer==3.4.2
requests==2.32.4
jaxlib==0.4.30
asttokens==3.0.0
tqdm==4.67.1
torch==2.7.1
pytweening==1.2.0
click==8.1.8
jax==0.4.30
tzdata==2025.2
pyobjc-framework-Quartz==11.0
setproctitle==1.3.6
numpy==1.26.4
httpcore==1.0.9
decorator==5.2.1
PyGetWindow==0.0.9
executing==2.2.0
pyperclip==1.9.0
stack-data==0.6.3
tensorboard==2.19.0
pandas==2.3.0
timm==1.0.15
sympy==1.14.0
Werkzeug==3.1.3
torchvision==0.22.1
Markdown==3.8
kiwisolver==1.4.7
jsonschema==4.24.0
setuptools==78.1.0
aiofiles==23.2.1
referencing==0.36.2
certifi==2025.4.26
contourpy==1.3.0
regex==2024.11.6
av==14.4.0
h11==0.16.0
gitdb==4.0.12
sniffio==1.3.1
transformers==4.36.2
mdurl==0.1.2
ffmpy==0.6.0
python-dateutil==2.9.0.post0
mpmath==1.3.0
qwen-vl-utils==0.0.11
ipython==8.18.1
wcwidth==0.2.13
huggingface-hub==0.32.5
prompt_toolkit==3.0.51
psutil==5.9.8
rpds-py==0.25.1
packaging==25.0
fastapi==0.115.12
urllib3==2.4.0
jsonschema-specifications==2025.4.1
PyRect==0.2.0
cycler==0.12.1
Jinja2==3.1.6
wheel==0.45.1
gradio_client==0.6.1
opencv-python==*********
filelock==3.18.0
python-multipart==0.0.20
pure_eval==0.2.3
MarkupSafe==2.1.5
pillow==10.4.0
ruff==0.11.13
sentry-sdk==2.29.1
networkx==3.2.1
fsspec==2025.5.1
wandb==0.20.1
absl-py==2.3.0
einops==0.8.1
PyYAML==6.0.2
httpx==0.28.1
rich==14.0.0
matplotlib-inline==0.1.7
smmap==5.0.2
importlib_metadata==8.7.0
safetensors==0.5.3
peft==0.7.1
tensorboard-data-server==0.7.2
orjson==3.10.18
spaces==0.37.0
pyobjc-core==11.0
tokenizers==0.15.2
ml_dtypes==0.5.1
altair==5.5.0
uvicorn==0.34.3
zipp==3.23.0
annotated-types==0.7.0
grpcio==1.73.0
eval_type_backport==0.2.2
accelerate==1.7.0
six==1.17.0
importlib_resources==6.5.2
PyAutoGUI==0.9.54
fonttools==4.58.2
