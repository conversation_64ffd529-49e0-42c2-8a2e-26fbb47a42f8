#!/bin/bash

# ShowUI-2B Fine-tuning Script for macOS
# 确保在ShowUI-main目录下运行此脚本

echo "开始ShowUI-2B微调训练..."

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 运行训练
cd ShowUI-main
python train.py \
    --model_id "showlab/ShowUI-2B" \
    --local_weight \
    --local_weight_dir "../ShowUI-2B" \
    --train_dataset "../showui_data/my_ui_grounding_dataset" \
    --train_json "../showui_data/my_ui_grounding_dataset/metadata/hf_train.json" \
    --dataset_dir "../showui_data" \
    --exp_id "showui_finetune_$(date +%Y%m%d_%H%M%S)" \
    --epochs 3 \
    --steps_per_epoch 100 \
    --batch_size 1 \
    --grad_accumulation_steps 4 \
    --lr 0.0001 \
    --warmup_steps 50 \
    --workers 4 \
    --precision "fp16" \
    --use_qlora \
    --lora_r 8 \
    --lora_alpha 16 \
    --gradient_checkpointing \
    --no_eval \
    --print_freq 10 \
    --log_base_dir "../runs"

echo "训练完成！"
